import { Types } from 'mongoose';

import { QueuesTask } from '@shared/services/Queues/schemas';
import { RportJob } from '@shared/services/Rport/schemas';

import { AppHostData, BasicApp } from '../models';

import { errors } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

import TasksFunctions from '@shared/services/Queues/helpers/functions/task.function';
import prescripts from '@shared/services/Rport/helpers/prescripts';
import { executeCommandOnClientsOrGroups } from '@shared/services/Rport/helpers/connections/rport';
import { getAndValidateClientsConnected } from '@shared/services/Rport/helpers/utils/rport-platform.utils';

import {
  API_BASE_URL,
  CDN_BASE_URL,
  ELASTIC_INVENTORY_INDEX,
  HOST_CLIENT_TOKEN,
} from '@shared/constants/env';

import { IN_PROGRESS, PENDING } from '@shared/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';
import { UNINSTALL_SOFTWARE_TIMEOUT } from '../constants';
import { TaskParams } from '@shared/services/Queues/helpers/types/task.types';
import { KnownQueuesTaskDocument } from '@shared/services/Queues/schemas/task.schema';
import { getOSNameFromKernel } from '@shared/services/Rport/helpers/functions';

export const uninstallAppFromHosts = async (
  hostWithApp: AppHostData[],
  app: BasicApp,
  user: Record<string, any> = {}
) => {
  // Getting hosts that are not uninstalling the app currently
  const hostWithoutTask = await getHostsWithoutUninstallAppTask(app, hostWithApp);

  if (hostWithoutTask.length === 0) {
    throw errors.already_exists('Uninstall task');
  }

  const { clientIdsConnected, clientIdsDisconnected } = await getAndValidateClientsConnected(
    hostWithoutTask.map((host) => host.rportId)
  );

  // Merge active with the uninstall string
  const activeHostsWithData = hostWithApp.filter((host) =>
    clientIdsConnected.includes(host.rportId)
  );
  const inactiveHostsWithData = hostWithApp.filter((host) =>
    clientIdsDisconnected.includes(host.rportId)
  );

  const job = await executeUninstallAppOnHosts(
    activeHostsWithData,
    inactiveHostsWithData,
    user.email || 'SYSTEM'
  );

  return job;
};

const getHostsWithoutUninstallAppTask = async (app: BasicApp, hostIds: AppHostData[]) => {
  const tasks = await QueuesTask.find<KnownQueuesTaskDocument<TASK_NAMES.UNINSTALL_SOFTWARE>>({
    name: TASK_NAMES.UNINSTALL_SOFTWARE,
    params: {
      rportIds: { $in: hostIds.map((host) => host.rportId) },
      softwareName: app.name,
      ...(app.vendor ? { softwareVendor: app.vendor } : {}),
      ...(app.version ? { softwareVersion: app.version } : {}),
    } as Partial<Record<keyof TaskParams<TASK_NAMES.UNINSTALL_SOFTWARE>, any>>,
    status: { $in: [PENDING, IN_PROGRESS] },
  });

  if (tasks === null || tasks.length === 0) {
    return hostIds;
  }

  const hostsWithTask = new Set<string>();

  tasks.forEach((task) => {
    task!.params.selectedHosts.forEach((host) => hostsWithTask.add(host));
  });

  const hostsWithoutTask = hostIds.filter((host) => !hostsWithTask.has(host.rportId));

  return hostsWithoutTask;
};

const executeUninstallAppOnHosts = async (
  activeHosts: AppHostData[],
  inactiveHosts: AppHostData[],
  author: string
) => {
  const jobIds = await runUninstallParallel(activeHosts);

  // Create Hosts with uninstalled app
  const rportJob = await RportJob.create({
    jobId: null,
    jobIds: jobIds || [],
    author: author,
    command: 'UNINSTALL_APPLICATION',
    timeout: UNINSTALL_SOFTWARE_TIMEOUT,
    os: getOSNameFromKernel('windows'),
    clients: activeHosts.map((host) => host.rportId) || [],
    clientsInQueue: inactiveHosts.map((host) => host.rportId) || [],
    useQueue: true,
  });

  if (inactiveHosts.length !== 0) {
    await createUninstallTask(inactiveHosts, rportJob._id);
  }

  return rportJob;
};

const createUninstallTask = async (hosts: AppHostData[], jobId: Types.ObjectId) => {
  const task = await TasksFunctions.createTask(TASK_NAMES.UNINSTALL_SOFTWARE, 1, {
    selectedHosts: hosts.map((host) => host.rportId),
    affectedHosts: [],
    softwareName: hosts[0].name,
    softwareVendor: hosts[0].vendor,
    softwareVersion: hosts[0].version,
    uninstallString: hosts[0].uninstallString,
    job: jobId,
  });

  return task;
};

export const runUninstallTask = async (selectedHosts: AppHostData[], jobId: Types.ObjectId) => {
  const jobIds = await runUninstallParallel(selectedHosts);

  // Update the job
  await RportJob.findByIdAndUpdate(jobId, {
    $push: {
      jobIds: { $each: jobIds }, // Add the job ids to the job
      clients: { $each: selectedHosts.map((host) => host.rportId) }, // Add the clients to the job
    },
    $pull: {
      clientsInQueue: { $in: selectedHosts.map((host) => host.rportId) }, // Remove the clients from the queue
    },
  });
};

const runUninstallParallel = async (selectedHosts: AppHostData[]) => {
  const uninstallPromises = selectedHosts.map(async (host) => {
    try {
      const jobId = await executeUninstallSoftware(host.rportId, host.uninstallString);
      return jobId?.jid || null;
    } catch (error) {
      Logger.error(`Failed to uninstall software on host ${host}:`, error);
      return null;
    }
  });

  const results = await Promise.allSettled(uninstallPromises);

  const jobIds: string[] = [];

  results.forEach((result) => {
    if (result.status === 'fulfilled' && result.value !== null) {
      jobIds.push(result.value as string);
    }
  });

  return jobIds;
};

const executeUninstallSoftware = async (hostId: string, uninstallString: string) => {
  let inventoryCommand = getUninstallCommand(uninstallString);
  if (prescripts.windows) {
    inventoryCommand = prescripts.windows.addPrescripts(inventoryCommand);
  }

  try {
    const result = await executeCommandOnClientsOrGroups(
      [hostId],
      [],
      inventoryCommand,
      UNINSTALL_SOFTWARE_TIMEOUT,
      'powershell',
      'C:\\Windows\\Temp'
    );
    return result as { jid: string };
  } catch (error) {
    Logger.error(`Failed to uninstall software on host ${hostId}:`, error);
    return null;
  }
};

const getUninstallCommand = (uninstallString: string) =>
  `$tempfile = "$(Get-Date -Format FileDateTime)-uninstall-app.ps1"; Invoke-WebRequest -Uri "${CDN_BASE_URL}/inventory/windows/Uninstall-Application.ps1" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -UninstallString '${uninstallString}'; Remove-Item $tempfile 2>&1 > $null; $tempfile = "$(Get-Date -Format FileDateTime)-get-inventory.ps1"; Invoke-WebRequest -Uri "${CDN_BASE_URL}/inventory/windows/Get-Inventory.ps1" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -gw "${API_BASE_URL}/gw/host" -ct "${HOST_CLIENT_TOKEN}" -in "${ELASTIC_INVENTORY_INDEX}" -cat "Applications"; Remove-Item $tempfile 2>&1 > $null`;
