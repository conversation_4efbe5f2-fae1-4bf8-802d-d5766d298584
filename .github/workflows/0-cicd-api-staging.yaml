name: STAGING Batuta API CICD - Build Push Docker Deploy ArgoCD
on:
  push:
    branches:
      - 'staging'
    paths:
      - '.github/workflows/*staging.yaml'
      - 'apps/api-users/**'
      - 'shared/**'
      - 'pnpm-lock.yaml'
      - 'pnpm-workspace.yaml'
      - 'package.json'
      - 'service.manifest.json'
  # pull_request:
  #   branches:
  #     - 'staging'
  #   types:
  #     - closed

jobs:
  call-ci-workflow-api-staging:
    if: github.ref == 'refs/heads/staging'
    uses: ./.github/workflows/1-build-image-staging.yaml
    with:
      ECR_REPOSITORY: batuta-staging-us-east-1-ecr-api-users
      AWS_REGION: us-east-1
      environment: staging
      dockerfile: apps/api-users/Dockerfile
      repository: batuta-api-users
    secrets: inherit

  # tag-docker-image-staging:
  #   if: github.ref == 'refs/heads/staging'
  #   uses: ./.github/workflows/2-cd-update-tag-staging.yaml
  #   needs: call-ci-workflow-staging
  #   with:
  #     environment: 'staging'
  #     service: batuta-soar
  #     tag: ${{needs.call-ci-workflow-staging.outputs.tag}}
  #     values-path: application/staging/clients
  #     tag-path: 'image.tag'
  #   secrets:
  #     token: ${{ secrets.GH_TOKEN }}

  # publish-scripts-staging:
  #   if: github.head_ref == 'dev' && github.ref == 'refs/heads/staging' && github.event.pull_request.merged == true
  #   uses: ./.github/workflows/3-publish-scripts-staging.yaml
  #   secrets:
  #     GH_TOKEN: ${{ secrets.GH_TOKEN }}
