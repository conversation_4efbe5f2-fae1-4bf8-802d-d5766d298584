name: Build Image and Push to ECR
on:
  workflow_call:
    inputs:
      ECR_REPOSITORY:
        required: true
        type: string
      AWS_REGION:
        required: true
        type: string
      environment:
        required: true
        type: string
      dockerfile:
        required: true
        type: string
      repository:
        required: true
        type: string
    outputs:
      tag:
        description: 'Tag of the new built docker image'
        value: ${{ jobs.build.outputs.tag }}

jobs:
  build:
    name: Build Image and Push to ECR
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
      contents: write
    env:
      ECR_REPOSITORY: ${{ inputs.ECR_REPOSITORY }}
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.set_github_run_number.outputs.outtag }}
    steps:
      - name: Checkout Git Code
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ inputs.AWS_REGION }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set GITHUB_RUN_NUMBER
        id: set_github_run_number
        run: |
          echo GITHUB_RUN_NUMBER=$((GITHUB_RUN_NUMBER + 279))>> $GITHUB_ENV
          echo "outtag=${{ inputs.repository }}-$((GITHUB_RUN_NUMBER + 279))" >> $GITHUB_OUTPUT

      - name: Build and push
        uses: docker/build-push-action@v5
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        with:
          context: .
          file: ${{ inputs.dockerfile }}
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:${{ inputs.repository }}-${{ env.GITHUB_RUN_NUMBER }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{ vars.AWS_ACCOUNT_ID }}.dkr.ecr.${{ inputs.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOSITORY }}:${{ inputs.repository }}-${{ env.GITHUB_RUN_NUMBER }}'
          format: 'table'
        env:
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ inputs.AWS_REGION }}
